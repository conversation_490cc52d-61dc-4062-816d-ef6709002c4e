"""
Search Service - vektorové vyhledávání v dokumentech
"""

from typing import List, Optional, Dict, Any
import structlog
from sentence_transformers import SentenceTransformer
import asyncpg
import json

logger = structlog.get_logger()

class SearchService:
    """Služba pro vektorové vyhledávání"""
    
    def __init__(self, database_url: str, redis_url: str, embedding_model: str = "all-MiniLM-L6-v2"):
        self.database_url = database_url
        self.redis_url = redis_url
        self.embedding_model_name = embedding_model
        self.embedding_model = None
        self.pool = None
    
    async def initialize(self):
        """Inicializace search služby"""
        try:
            # Inicializace databázového poolu
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # Načtení embedding modelu
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            
            logger.info("Search Service inicializován")
            
        except Exception as e:
            logger.error("Chyba při inicializaci Search Service", error=str(e))
            raise
    
    async def cleanup(self):
        """Úklid při ukončování"""
        if self.pool:
            await self.pool.close()
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generování embedding pro text"""
        if not self.embedding_model:
            raise RuntimeError("Embedding model není inicializován")
        
        embedding = self.embedding_model.encode(text)
        return embedding.tolist()
    
    async def search(
        self,
        query: str,
        personality_id: Optional[str] = None,
        document_types: Optional[List[str]] = None,
        top_k: int = 10,
        similarity_threshold: float = 0.6
    ) -> List[Dict[str, Any]]:
        """Vektorové vyhledávání v dokumentech"""
        
        try:
            # Generování embedding pro dotaz
            query_embedding = self._generate_embedding(query)
            
            async with self.pool.acquire() as conn:
                # Základní dotaz
                sql_query = """
                    SELECT 
                        dc.id,
                        dc.content,
                        dc.metadata,
                        d.title,
                        d.document_type,
                        d.personality_id,
                        1 - (ce.embedding <=> $1) as similarity
                    FROM document_chunks dc
                    JOIN chunk_embeddings ce ON dc.id = ce.chunk_id
                    JOIN documents d ON dc.document_id = d.id
                    WHERE 1 - (ce.embedding <=> $1) >= $2
                """
                params = [query_embedding, similarity_threshold]
                param_index = 3
                
                # Přidání filtrů
                if personality_id:
                    sql_query += f" AND d.personality_id = ${param_index}"
                    params.append(personality_id)
                    param_index += 1
                
                if document_types:
                    sql_query += f" AND d.document_type = ANY(${param_index})"
                    params.append(document_types)
                    param_index += 1
                
                sql_query += f" ORDER BY similarity DESC LIMIT ${param_index}"
                params.append(top_k)
                
                rows = await conn.fetch(sql_query, *params)
                
                results = []
                for row in rows:
                    results.append({
                        "id": str(row["id"]),
                        "content": row["content"],
                        "metadata": {
                            **json.loads(row["metadata"]) if row["metadata"] else {},
                            "document_title": row["title"],
                            "document_type": row["document_type"],
                            "personality_id": str(row["personality_id"]) if row["personality_id"] else None
                        },
                        "similarity_score": float(row["similarity"])
                    })
                
                logger.info("Vyhledávání dokončeno", query=query, results_count=len(results))
                return results
                
        except Exception as e:
            logger.error("Chyba při vyhledávání", error=str(e))
            return []
