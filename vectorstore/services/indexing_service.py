"""
Indexing Service - zpracování a indexování dokumentů
"""

import os
import hashlib
from typing import List, Optional, Dict, Any
import structlog
from sentence_transformers import SentenceTransformer
import asyncpg
import json
import uuid

logger = structlog.get_logger()

class IndexingService:
    """Služba pro indexování dokumentů"""
    
    def __init__(self, database_url: str, redis_url: str, embedding_model: str = "all-MiniLM-L6-v2"):
        self.database_url = database_url
        self.redis_url = redis_url
        self.embedding_model_name = embedding_model
        self.embedding_model = None
        self.pool = None
    
    async def initialize(self):
        """Inicializace indexing služby"""
        try:
            # Inicializace databázového poolu
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # Načtení embedding modelu
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            
            logger.info("Indexing Service inicializován")
            
        except Exception as e:
            logger.error("Chyba při inicializaci Indexing Service", error=str(e))
            raise
    
    async def cleanup(self):
        """Úklid při ukončování"""
        if self.pool:
            await self.pool.close()
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generování embedding pro text"""
        if not self.embedding_model:
            raise RuntimeError("Embedding model není inicializován")
        
        embedding = self.embedding_model.encode(text)
        return embedding.tolist()
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Výpočet hash souboru"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Rozdělení textu na chunky"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Pokusit se najít konec věty
            if end < len(text):
                # Hledat zpětně nejbližší tečku, vykřičník nebo otazník
                for i in range(end, max(start + chunk_size // 2, start), -1):
                    if text[i] in '.!?':
                        end = i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks
    
    async def process_document(
        self,
        file_path: str,
        title: str,
        personality_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Zpracování a indexování dokumentu"""
        
        try:
            # Čtení obsahu souboru
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Výpočet hash a velikosti
            file_hash = self._calculate_file_hash(file_path)
            file_size = os.path.getsize(file_path)
            filename = os.path.basename(file_path)
            
            # Detekce typu dokumentu
            document_type = self._detect_document_type(filename)
            
            async with self.pool.acquire() as conn:
                # Kontrola, zda dokument již existuje
                existing_doc = await conn.fetchrow("""
                    SELECT id FROM documents WHERE file_hash = $1
                """, file_hash)
                
                if existing_doc:
                    logger.info("Dokument již existuje", file_hash=file_hash)
                    return str(existing_doc["id"])
                
                # Uložení dokumentu
                document_id = str(uuid.uuid4())
                await conn.execute("""
                    INSERT INTO documents (
                        id, title, filename, content, document_type,
                        personality_id, metadata, file_size, file_hash, is_processed
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                """,
                    document_id, title, filename, content, document_type,
                    personality_id, json.dumps(metadata or {}), file_size, file_hash, False
                )
                
                # Rozdělení na chunky
                chunks = self._chunk_text(content)
                
                # Zpracování chunků
                for i, chunk_content in enumerate(chunks):
                    chunk_id = str(uuid.uuid4())
                    
                    # Uložení chunku
                    await conn.execute("""
                        INSERT INTO document_chunks (
                            id, document_id, content, chunk_index, metadata
                        ) VALUES ($1, $2, $3, $4, $5)
                    """,
                        chunk_id, document_id, chunk_content, i,
                        json.dumps({"chunk_size": len(chunk_content)})
                    )
                    
                    # Generování embedding
                    embedding = self._generate_embedding(chunk_content)
                    
                    # Uložení embedding
                    await conn.execute("""
                        INSERT INTO chunk_embeddings (
                            chunk_id, embedding, metadata
                        ) VALUES ($1, $2, $3)
                    """,
                        chunk_id, embedding,
                        json.dumps({
                            "document_id": document_id,
                            "chunk_index": i,
                            "document_type": document_type,
                            "personality_id": personality_id
                        })
                    )
                
                # Označení dokumentu jako zpracovaného
                await conn.execute("""
                    UPDATE documents SET is_processed = TRUE WHERE id = $1
                """, document_id)
                
                logger.info("Dokument zpracován", document_id=document_id, chunks_count=len(chunks))
                return document_id
                
        except Exception as e:
            logger.error("Chyba při zpracování dokumentu", error=str(e), file_path=file_path)
            raise
    
    def _detect_document_type(self, filename: str) -> str:
        """Detekce typu dokumentu podle přípony"""
        extension = filename.lower().split('.')[-1] if '.' in filename else ''
        
        type_mapping = {
            'txt': 'text',
            'md': 'markdown',
            'pdf': 'pdf',
            'docx': 'word',
            'doc': 'word',
            'html': 'html',
            'htm': 'html',
            'json': 'json',
            'csv': 'csv'
        }
        
        return type_mapping.get(extension, 'unknown')
