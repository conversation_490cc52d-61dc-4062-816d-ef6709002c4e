"""
RAG Service - základní implementace pro Retrieval-Augmented Generation
"""

from typing import List, Optional, Dict, Any
import structlog
import httpx

logger = structlog.get_logger()

class RAGService:
    """Služba pro RAG (Retrieval-Augmented Generation)"""
    
    def __init__(self, indexing_service, search_service, llm_service_url: str):
        self.indexing_service = indexing_service
        self.search_service = search_service
        self.llm_service_url = llm_service_url
        self.http_client = None
    
    async def initialize(self):
        """Inicializace RAG služby"""
        self.http_client = httpx.AsyncClient(timeout=30.0)
        logger.info("RAG Service inicializován")
    
    async def cleanup(self):
        """Úklid při ukončování"""
        if self.http_client:
            await self.http_client.aclose()
    
    async def query(
        self,
        query: str,
        personality_id: Optional[str] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        include_metadata: bool = True,
        rerank: bool = True
    ) -> Dict[str, Any]:
        """RAG dotaz s generováním odpovědi"""
        
        try:
            # 1. Vyhledání relevantních dokumentů
            search_results = await self.search_service.search(
                query=query,
                personality_id=personality_id,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            # 2. Sestavení kontextu
            context_parts = []
            sources = []
            
            for result in search_results:
                context_parts.append(result["content"])
                sources.append({
                    "id": result["id"],
                    "content": result["content"][:200] + "..." if len(result["content"]) > 200 else result["content"],
                    "metadata": result["metadata"] if include_metadata else {},
                    "similarity_score": result.get("similarity_score")
                })
            
            context = "\n\n".join(context_parts)
            
            # 3. Generování odpovědi pomocí LLM
            llm_request = {
                "messages": [
                    {
                        "role": "system",
                        "content": "Odpověz na otázku na základě poskytnutého kontextu. Pokud informace není v kontextu, řekni to."
                    },
                    {
                        "role": "user",
                        "content": f"Kontext:\n{context}\n\nOtázka: {query}"
                    }
                ],
                "model": "gpt-3.5-turbo",
                "max_tokens": 300,
                "temperature": 0.7
            }
            
            if self.http_client:
                llm_response = await self.http_client.post(
                    f"{self.llm_service_url}/llm/chat",
                    json=llm_request
                )
                
                if llm_response.status_code == 200:
                    llm_data = llm_response.json()
                    answer = llm_data["message"]["content"]
                else:
                    answer = "Omlouvám se, ale nemohu vygenerovat odpověď v tuto chvíli."
            else:
                answer = "LLM služba není dostupná."
            
            return {
                "query": query,
                "answer": answer,
                "sources": sources,
                "metadata": {
                    "total_sources": len(sources),
                    "similarity_threshold": similarity_threshold,
                    "rerank_used": rerank
                }
            }
            
        except Exception as e:
            logger.error("Chyba při RAG dotazu", error=str(e))
            return {
                "query": query,
                "answer": f"Chyba při zpracování dotazu: {str(e)}",
                "sources": [],
                "metadata": {"error": str(e)}
            }
