"""
Document Parser - parso<PERSON><PERSON> různ<PERSON>ch formátů dokumentů
"""

import os
from typing import Dict, Any, Optional
import structlog

logger = structlog.get_logger()

class DocumentParser:
    """Parser pro různé formáty dokumentů"""
    
    def __init__(self):
        self.parsers = {}
    
    async def initialize(self):
        """Inicializace parseru"""
        logger.info("Document Parser inicializován")
    
    def parse_document(self, file_path: str) -> Dict[str, Any]:
        """Parsování dokumentu podle typu"""
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Soubor {file_path} neexistuje")
        
        # Detekce typu souboru
        file_extension = file_path.lower().split('.')[-1] if '.' in file_path else ''
        
        try:
            if file_extension == 'txt':
                return self._parse_text(file_path)
            elif file_extension == 'md':
                return self._parse_markdown(file_path)
            elif file_extension == 'pdf':
                return self._parse_pdf(file_path)
            elif file_extension in ['doc', 'docx']:
                return self._parse_word(file_path)
            elif file_extension in ['html', 'htm']:
                return self._parse_html(file_path)
            else:
                # Pokus o čtení jako text
                return self._parse_text(file_path)
                
        except Exception as e:
            logger.error("Chyba při parsování dokumentu", error=str(e), file_path=file_path)
            raise
    
    def _parse_text(self, file_path: str) -> Dict[str, Any]:
        """Parsování textového souboru"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "content": content,
            "metadata": {
                "type": "text",
                "encoding": "utf-8",
                "lines": len(content.split('\n')),
                "characters": len(content)
            }
        }
    
    def _parse_markdown(self, file_path: str) -> Dict[str, Any]:
        """Parsování Markdown souboru"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Základní extrakce metadat z Markdown
        lines = content.split('\n')
        title = None
        
        # Hledání nadpisu
        for line in lines:
            if line.startswith('# '):
                title = line[2:].strip()
                break
        
        return {
            "content": content,
            "metadata": {
                "type": "markdown",
                "title": title,
                "lines": len(lines),
                "characters": len(content)
            }
        }
    
    def _parse_pdf(self, file_path: str) -> Dict[str, Any]:
        """Parsování PDF souboru"""
        try:
            import PyPDF2
            
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                
                content_parts = []
                for page in pdf_reader.pages:
                    content_parts.append(page.extract_text())
                
                content = '\n'.join(content_parts)
                
                return {
                    "content": content,
                    "metadata": {
                        "type": "pdf",
                        "pages": len(pdf_reader.pages),
                        "characters": len(content)
                    }
                }
                
        except ImportError:
            logger.warning("PyPDF2 není nainstalováno, používám textový parser")
            return self._parse_text(file_path)
        except Exception as e:
            logger.error("Chyba při parsování PDF", error=str(e))
            # Fallback na textový parser
            return self._parse_text(file_path)
    
    def _parse_word(self, file_path: str) -> Dict[str, Any]:
        """Parsování Word dokumentu"""
        try:
            from docx import Document
            
            doc = Document(file_path)
            
            content_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text)
            
            content = '\n'.join(content_parts)
            
            return {
                "content": content,
                "metadata": {
                    "type": "word",
                    "paragraphs": len(doc.paragraphs),
                    "characters": len(content)
                }
            }
            
        except ImportError:
            logger.warning("python-docx není nainstalováno, používám textový parser")
            return self._parse_text(file_path)
        except Exception as e:
            logger.error("Chyba při parsování Word dokumentu", error=str(e))
            # Fallback na textový parser
            return self._parse_text(file_path)
    
    def _parse_html(self, file_path: str) -> Dict[str, Any]:
        """Parsování HTML souboru"""
        try:
            from bs4 import BeautifulSoup
            
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Odstranění skriptů a stylů
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extrakce textu
            content = soup.get_text()
            
            # Vyčištění textu
            lines = (line.strip() for line in content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            content = '\n'.join(chunk for chunk in chunks if chunk)
            
            # Extrakce titulu
            title = None
            title_tag = soup.find('title')
            if title_tag:
                title = title_tag.get_text().strip()
            
            return {
                "content": content,
                "metadata": {
                    "type": "html",
                    "title": title,
                    "characters": len(content)
                }
            }
            
        except ImportError:
            logger.warning("BeautifulSoup není nainstalováno, používám textový parser")
            return self._parse_text(file_path)
        except Exception as e:
            logger.error("Chyba při parsování HTML", error=str(e))
            # Fallback na textový parser
            return self._parse_text(file_path)
