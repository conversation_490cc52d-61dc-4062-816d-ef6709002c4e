"""
NESTOR RAG Service (Vectorstore)
Služba pro Retrieval-Augmented Generation s vektorovým vyhledáváním
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import List, Optional, Dict, Any, Union
import tempfile
import hashlib

from fastapi import FastAP<PERSON>, HTTPException, Depends, status, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import structlog
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

from services.rag_service import RAGService
from services.indexing_service import IndexingService
from services.search_service import SearchService
from parsers.document_parser import DocumentParser

# Konfigurace loggingu
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Metriky
REQUEST_COUNT = Counter('rag_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('rag_request_duration_seconds', 'Request duration')
DOCUMENT_COUNT = Counter('rag_documents_processed_total', 'Total documents processed')

# Pydantic modely
class DocumentUpload(BaseModel):
    title: str = Field(..., description="Název dokumentu")
    personality_id: Optional[str] = Field(None, description="ID digitální osobnosti")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadata dokumentu")

class RAGQuery(BaseModel):
    query: str = Field(..., description="Dotaz pro RAG")
    personality_id: Optional[str] = Field(None, description="ID digitální osobnosti")
    top_k: int = Field(default=5, ge=1, le=20, description="Počet dokumentů k načtení")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Práh podobnosti")
    include_metadata: bool = Field(default=True, description="Zahrnout metadata")
    rerank: bool = Field(default=True, description="Použít re-ranking")

class SearchQuery(BaseModel):
    query: str = Field(..., description="Vyhledávací dotaz")
    personality_id: Optional[str] = Field(None, description="ID digitální osobnosti")
    document_types: Optional[List[str]] = Field(None, description="Typy dokumentů")
    top_k: int = Field(default=10, ge=1, le=50, description="Počet výsledků")
    similarity_threshold: float = Field(default=0.6, ge=0.0, le=1.0, description="Práh podobnosti")

class DocumentChunk(BaseModel):
    id: str
    content: str
    metadata: Dict[str, Any]
    similarity_score: Optional[float] = None

class RAGResponse(BaseModel):
    query: str
    answer: str
    sources: List[DocumentChunk]
    metadata: Dict[str, Any]

class SearchResponse(BaseModel):
    query: str
    results: List[DocumentChunk]
    total_results: int
    metadata: Dict[str, Any]

class HealthResponse(BaseModel):
    status: str
    version: str
    services: Dict[str, str]

# Globální proměnné
rag_service: Optional[RAGService] = None
indexing_service: Optional[IndexingService] = None
search_service: Optional[SearchService] = None
document_parser: Optional[DocumentParser] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifecycle management pro FastAPI aplikaci"""
    global rag_service, indexing_service, search_service, document_parser
    
    logger.info("Spouštění RAG Service...")
    
    # Inicializace služeb
    document_parser = DocumentParser()
    
    indexing_service = IndexingService(
        database_url=os.getenv("DATABASE_URL", "*************************************************/nestor"),
        redis_url=os.getenv("REDIS_URL", "redis://redis:6379")
    )
    
    search_service = SearchService(
        database_url=os.getenv("DATABASE_URL", "*************************************************/nestor"),
        redis_url=os.getenv("REDIS_URL", "redis://redis:6379")
    )
    
    rag_service = RAGService(
        indexing_service=indexing_service,
        search_service=search_service,
        llm_service_url=os.getenv("LLM_SERVICE_URL", "http://llm_interface:8000")
    )
    
    # Inicializace všech služeb
    await document_parser.initialize()
    await indexing_service.initialize()
    await search_service.initialize()
    await rag_service.initialize()
    
    logger.info("RAG Service úspěšně spuštěn")
    
    yield
    
    logger.info("Ukončování RAG Service...")
    if rag_service:
        await rag_service.cleanup()
    if search_service:
        await search_service.cleanup()
    if indexing_service:
        await indexing_service.cleanup()

# Vytvoření FastAPI aplikace
app = FastAPI(
    title="NESTOR RAG Service",
    description="Služba pro Retrieval-Augmented Generation s vektorovým vyhledáváním",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # V produkci nastavit konkrétní domény
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_rag_service() -> RAGService:
    """Dependency pro získání RAG service"""
    if rag_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="RAG service není inicializován"
        )
    return rag_service

def get_search_service() -> SearchService:
    """Dependency pro získání search service"""
    if search_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Search service není inicializován"
        )
    return search_service

def get_indexing_service() -> IndexingService:
    """Dependency pro získání indexing service"""
    if indexing_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Indexing service není inicializován"
        )
    return indexing_service

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        services={
            "postgres": "connected",
            "redis": "connected",
            "vector_storage": "ready",
            "document_parser": "ready"
        }
    )

@app.get("/metrics")
async def metrics():
    """Prometheus metriky"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@app.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(...),
    personality_id: Optional[str] = Form(None),
    metadata: str = Form("{}"),
    indexing_svc: IndexingService = Depends(get_indexing_service)
):
    """Upload a zpracování dokumentu"""
    REQUEST_COUNT.labels(method="POST", endpoint="/documents/upload").inc()
    
    try:
        with REQUEST_DURATION.time():
            # Validace souboru
            if not file.filename:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Název souboru je povinný"
                )
            
            # Uložení do dočasného souboru
            with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as tmp_file:
                content = await file.read()
                tmp_file.write(content)
                tmp_file_path = tmp_file.name
            
            try:
                # Parsování metadat
                import json
                doc_metadata = json.loads(metadata) if metadata != "{}" else {}
                
                # Zpracování dokumentu
                document_id = await indexing_svc.process_document(
                    file_path=tmp_file_path,
                    title=title,
                    personality_id=personality_id,
                    metadata=doc_metadata
                )
                
                DOCUMENT_COUNT.inc()
                logger.info("Dokument zpracován", document_id=document_id, filename=file.filename)
                
                return {
                    "document_id": document_id,
                    "title": title,
                    "filename": file.filename,
                    "status": "processed"
                }
                
            finally:
                # Smazání dočasného souboru
                os.unlink(tmp_file_path)
                
    except Exception as e:
        logger.error("Chyba při zpracování dokumentu", error=str(e), filename=file.filename)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při zpracování dokumentu: {str(e)}"
        )

@app.post("/rag/query", response_model=RAGResponse)
async def rag_query(
    query_data: RAGQuery,
    rag_svc: RAGService = Depends(get_rag_service)
):
    """RAG dotaz s generováním odpovědi"""
    REQUEST_COUNT.labels(method="POST", endpoint="/rag/query").inc()
    
    try:
        with REQUEST_DURATION.time():
            response = await rag_svc.query(
                query=query_data.query,
                personality_id=query_data.personality_id,
                top_k=query_data.top_k,
                similarity_threshold=query_data.similarity_threshold,
                include_metadata=query_data.include_metadata,
                rerank=query_data.rerank
            )
            
            logger.info("RAG dotaz zpracován", query=query_data.query, sources_count=len(response["sources"]))
            
            return RAGResponse(
                query=response["query"],
                answer=response["answer"],
                sources=[DocumentChunk(**source) for source in response["sources"]],
                metadata=response["metadata"]
            )
            
    except Exception as e:
        logger.error("Chyba při RAG dotazu", error=str(e), query=query_data.query)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při RAG dotazu: {str(e)}"
        )

@app.post("/search/query", response_model=SearchResponse)
async def search_query(
    query_data: SearchQuery,
    search_svc: SearchService = Depends(get_search_service)
):
    """Vektorové vyhledávání v dokumentech"""
    REQUEST_COUNT.labels(method="POST", endpoint="/search/query").inc()
    
    try:
        with REQUEST_DURATION.time():
            results = await search_svc.search(
                query=query_data.query,
                personality_id=query_data.personality_id,
                document_types=query_data.document_types,
                top_k=query_data.top_k,
                similarity_threshold=query_data.similarity_threshold
            )
            
            logger.info("Vyhledávání dokončeno", query=query_data.query, results_count=len(results))
            
            return SearchResponse(
                query=query_data.query,
                results=[DocumentChunk(**result) for result in results],
                total_results=len(results),
                metadata={"search_type": "vector", "threshold": query_data.similarity_threshold}
            )
            
    except Exception as e:
        logger.error("Chyba při vyhledávání", error=str(e), query=query_data.query)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při vyhledávání: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
