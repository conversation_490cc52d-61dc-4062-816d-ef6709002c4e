# NESTOR Environment Variables
# Zkopírujte tento soubor jako .env a upravte hodnoty podle potřeby

# Database Configuration
DATABASE_URL=postgresql://nestor:nestor_password@localhost:5432/nestor
POSTGRES_DB=nestor
POSTGRES_USER=nestor
POSTGRES_PASSWORD=nestor_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET=your-jwt-secret-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# LLM Services API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# Service URLs (for internal communication)
MEMORY_SERVICE_URL=http://localhost:8001
RAG_SERVICE_URL=http://localhost:8002
LLM_SERVICE_URL=http://localhost:8003

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Vector Database Configuration
VECTOR_DIMENSION=1536
VECTOR_SIMILARITY_THRESHOLD=0.7

# RAG Configuration
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200
RAG_TOP_K=5
RAG_RERANK_TOP_K=3

# Memory Configuration
MEMORY_MAX_CONTEXT_LENGTH=4000
MEMORY_RETENTION_DAYS=365

# Model Configuration
DEFAULT_LLM_MODEL=gpt-3.5-turbo
DEFAULT_EMBEDDING_MODEL=text-embedding-ada-002
LOCAL_MODEL_PATH=/app/models

# Development/Production
ENVIRONMENT=development
DEBUG=true

# Monitoring
PROMETHEUS_ENABLED=true
METRICS_PORT=9090

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["pdf", "txt", "docx", "md"]

# Blockchain (for tokenization)
BLOCKCHAIN_NETWORK=ethereum
BLOCKCHAIN_RPC_URL=https://mainnet.infura.io/v3/your-infura-key
PRIVATE_KEY=your-private-key-for-blockchain

# HashiCorp Vault (for production)
VAULT_URL=http://localhost:8200
VAULT_TOKEN=your-vault-token
VAULT_ENABLED=false
