"""
NESTOR Main API Service
Hlavní API služba pro komunikaci s frontendovými aplikacemi
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import List, Optional, Dict, Any

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import H<PERSON><PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import structlog
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response
import httpx

# Konfigurace loggingu
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Metriky
REQUEST_COUNT = Counter('api_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('api_request_duration_seconds', 'Request duration')

# Security
security = HTTPBearer()

# Pydantic modely
class HealthResponse(BaseModel):
    status: str
    version: str
    services: Dict[str, str]

class PersonalityCreate(BaseModel):
    name: str = Field(..., description="Název digitální osobnosti")
    description: Optional[str] = Field(None, description="Popis osobnosti")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadata")

class PersonalityResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str

class ChatMessage(BaseModel):
    role: str = Field(..., description="Role zprávy (user, assistant)")
    content: str = Field(..., description="Obsah zprávy")

class ChatRequest(BaseModel):
    message: str = Field(..., description="Zpráva uživatele")
    personality_id: str = Field(..., description="ID digitální osobnosti")
    context: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Kontext konverzace")

class ChatResponse(BaseModel):
    message: str
    personality_id: str
    context: Dict[str, Any]
    sources: List[Dict[str, Any]]

class MemoryRequest(BaseModel):
    content: str = Field(..., description="Obsah paměti")
    memory_type: str = Field(..., description="Typ paměti")
    importance: float = Field(default=0.5, ge=0.0, le=1.0, description="Důležitost")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadata")

# Service URLs
MEMORY_SERVICE_URL = os.getenv("MEMORY_SERVICE_URL", "http://memory:8000")
RAG_SERVICE_URL = os.getenv("RAG_SERVICE_URL", "http://vectorstore:8000")
LLM_SERVICE_URL = os.getenv("LLM_SERVICE_URL", "http://llm_interface:8000")

# HTTP klient
http_client: Optional[httpx.AsyncClient] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifecycle management pro FastAPI aplikaci"""
    global http_client
    
    logger.info("Spouštění Main API Service...")
    
    # Inicializace HTTP klienta
    http_client = httpx.AsyncClient(timeout=30.0)
    
    logger.info("Main API Service úspěšně spuštěn")
    
    yield
    
    logger.info("Ukončování Main API Service...")
    if http_client:
        await http_client.aclose()

# Vytvoření FastAPI aplikace
app = FastAPI(
    title="NESTOR Main API",
    description="Hlavní API služba pro komunikaci s frontendovými aplikacemi",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # V produkci nastavit konkrétní domény
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_http_client() -> httpx.AsyncClient:
    """Dependency pro získání HTTP klienta"""
    if http_client is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="HTTP klient není inicializován"
        )
    return http_client

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Ověření JWT tokenu (zjednodušená implementace)"""
    # TODO: Implementovat skutečné ověření JWT tokenu
    return {"user_id": "test_user", "username": "test"}

@app.get("/health", response_model=HealthResponse)
async def health_check(client: httpx.AsyncClient = Depends(get_http_client)):
    """Health check endpoint"""
    services = {}
    
    # Kontrola dostupnosti mikroslužeb
    try:
        response = await client.get(f"{MEMORY_SERVICE_URL}/health", timeout=5.0)
        services["memory"] = "healthy" if response.status_code == 200 else "unhealthy"
    except:
        services["memory"] = "unavailable"
    
    try:
        response = await client.get(f"{RAG_SERVICE_URL}/health", timeout=5.0)
        services["rag"] = "healthy" if response.status_code == 200 else "unhealthy"
    except:
        services["rag"] = "unavailable"
    
    try:
        response = await client.get(f"{LLM_SERVICE_URL}/health", timeout=5.0)
        services["llm"] = "healthy" if response.status_code == 200 else "unhealthy"
    except:
        services["llm"] = "unavailable"
    
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        services=services
    )

@app.get("/metrics")
async def metrics():
    """Prometheus metriky"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@app.post("/personalities", response_model=PersonalityResponse)
async def create_personality(
    personality_data: PersonalityCreate,
    current_user: dict = Depends(verify_token),
    client: httpx.AsyncClient = Depends(get_http_client)
):
    """Vytvoření nové digitální osobnosti"""
    REQUEST_COUNT.labels(method="POST", endpoint="/personalities").inc()
    
    try:
        with REQUEST_DURATION.time():
            # TODO: Implementovat vytvoření osobnosti v databázi
            # Pro demo účely vracíme mock data
            
            personality = {
                "id": "test_personality_id",
                "name": personality_data.name,
                "description": personality_data.description,
                "metadata": personality_data.metadata,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
            
            logger.info("Osobnost vytvořena", personality_id=personality["id"], name=personality_data.name)
            return PersonalityResponse(**personality)
            
    except Exception as e:
        logger.error("Chyba při vytváření osobnosti", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při vytváření osobnosti: {str(e)}"
        )

@app.post("/personalities/{personality_id}/chat", response_model=ChatResponse)
async def chat_with_personality(
    personality_id: str,
    chat_request: ChatRequest,
    current_user: dict = Depends(verify_token),
    client: httpx.AsyncClient = Depends(get_http_client)
):
    """Chat s digitální osobností"""
    REQUEST_COUNT.labels(method="POST", endpoint="/personalities/chat").inc()
    
    try:
        with REQUEST_DURATION.time():
            # 1. Vyhledání relevantních pamětí
            memory_query = {
                "query": chat_request.message,
                "personality_id": personality_id,
                "top_k": 5,
                "similarity_threshold": 0.7
            }
            
            memory_response = await client.post(
                f"{MEMORY_SERVICE_URL}/memory/query",
                json=memory_query
            )
            memories = memory_response.json() if memory_response.status_code == 200 else []
            
            # 2. RAG dotaz pro dodatečný kontext
            rag_query = {
                "query": chat_request.message,
                "personality_id": personality_id,
                "top_k": 3,
                "similarity_threshold": 0.7
            }
            
            rag_response = await client.post(
                f"{RAG_SERVICE_URL}/rag/query",
                json=rag_query
            )
            rag_data = rag_response.json() if rag_response.status_code == 200 else {}
            
            # 3. Sestavení kontextu pro LLM
            context_parts = []
            
            if memories:
                context_parts.append("Relevantní paměti:")
                for memory in memories[:3]:
                    context_parts.append(f"- {memory.get('content', '')}")
            
            if rag_data.get('sources'):
                context_parts.append("\nRelevantní dokumenty:")
                for source in rag_data['sources'][:2]:
                    context_parts.append(f"- {source.get('content', '')}")
            
            context = "\n".join(context_parts)
            
            # 4. Generování odpovědi pomocí LLM
            llm_request = {
                "messages": [
                    {
                        "role": "system",
                        "content": f"Jsi digitální osobnost s ID {personality_id}. Odpovídej na základě následujícího kontextu:\n\n{context}"
                    },
                    {
                        "role": "user",
                        "content": chat_request.message
                    }
                ],
                "model": "gpt-3.5-turbo",
                "max_tokens": 200,
                "temperature": 0.7
            }
            
            llm_response = await client.post(
                f"{LLM_SERVICE_URL}/llm/chat",
                json=llm_request
            )
            
            if llm_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Chyba při generování odpovědi"
                )
            
            llm_data = llm_response.json()
            assistant_message = llm_data["message"]["content"]
            
            # 5. Uložení konverzace do paměti
            memory_data = {
                "content": f"User: {chat_request.message}\nAssistant: {assistant_message}",
                "memory_type": "short_term",
                "personality_id": personality_id,
                "importance": 0.6,
                "metadata": {
                    "conversation_id": chat_request.context.get("conversation_id"),
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
            
            await client.post(
                f"{MEMORY_SERVICE_URL}/memory/add",
                json=memory_data
            )
            
            logger.info("Chat dokončen", personality_id=personality_id, message_length=len(assistant_message))
            
            return ChatResponse(
                message=assistant_message,
                personality_id=personality_id,
                context=chat_request.context,
                sources=rag_data.get('sources', [])
            )
            
    except Exception as e:
        logger.error("Chyba při chatu", error=str(e), personality_id=personality_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při chatu: {str(e)}"
        )

@app.post("/personalities/{personality_id}/memory")
async def add_memory(
    personality_id: str,
    memory_request: MemoryRequest,
    current_user: dict = Depends(verify_token),
    client: httpx.AsyncClient = Depends(get_http_client)
):
    """Přidání paměti k digitální osobnosti"""
    REQUEST_COUNT.labels(method="POST", endpoint="/personalities/memory").inc()
    
    try:
        with REQUEST_DURATION.time():
            memory_data = {
                "content": memory_request.content,
                "memory_type": memory_request.memory_type,
                "personality_id": personality_id,
                "importance": memory_request.importance,
                "metadata": memory_request.metadata
            }
            
            response = await client.post(
                f"{MEMORY_SERVICE_URL}/memory/add",
                json=memory_data
            )
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Chyba při přidávání paměti"
                )
            
            memory_response = response.json()
            logger.info("Paměť přidána", personality_id=personality_id, memory_id=memory_response["id"])
            
            return memory_response
            
    except Exception as e:
        logger.error("Chyba při přidávání paměti", error=str(e), personality_id=personality_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při přidávání paměti: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
