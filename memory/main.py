"""
NESTOR Memory Context Processor (MCP)
Služba pro správu a organizaci paměti a kontextu digitálních osobností
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import List, Optional, Dict, Any

from fastapi import <PERSON><PERSON>I, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import structlog
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

from core.memory_manager import MemoryManager
from core.memory_types import MemoryType, Memory
from storage.postgres_storage import PostgresStorage
from storage.vector_storage import VectorStorage
from storage.cache_storage import CacheStorage

# Konfigurace loggingu
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Metriky
REQUEST_COUNT = Counter('mcp_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('mcp_request_duration_seconds', 'Request duration')

# Pydantic modely
class MemoryCreate(BaseModel):
    content: str = Field(..., description="Obsah paměti")
    memory_type: MemoryType = Field(..., description="Typ paměti")
    personality_id: Optional[str] = Field(None, description="ID digitální osobnosti")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadata")
    importance: float = Field(default=0.5, ge=0.0, le=1.0, description="Důležitost (0-1)")

class MemoryUpdate(BaseModel):
    content: Optional[str] = None
    importance: Optional[float] = Field(None, ge=0.0, le=1.0)
    metadata: Optional[Dict[str, Any]] = None

class MemoryQuery(BaseModel):
    query: str = Field(..., description="Dotaz pro vyhledávání")
    memory_types: Optional[List[MemoryType]] = Field(None, description="Typy paměti k prohledání")
    personality_id: Optional[str] = Field(None, description="ID digitální osobnosti")
    top_k: int = Field(default=5, ge=1, le=50, description="Počet výsledků")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Práh podobnosti")

class MemoryResponse(BaseModel):
    id: str
    content: str
    memory_type: MemoryType
    personality_id: Optional[str]
    metadata: Dict[str, Any]
    importance: float
    created_at: str
    updated_at: str
    similarity_score: Optional[float] = None

class HealthResponse(BaseModel):
    status: str
    version: str
    services: Dict[str, str]

# Globální proměnné
memory_manager: Optional[MemoryManager] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifecycle management pro FastAPI aplikaci"""
    global memory_manager
    
    logger.info("Spouštění Memory Context Processor...")
    
    # Inicializace storage komponent
    postgres_storage = PostgresStorage(
        database_url=os.getenv("DATABASE_URL", "*************************************************/nestor")
    )
    
    vector_storage = VectorStorage(
        database_url=os.getenv("DATABASE_URL", "*************************************************/nestor")
    )
    
    cache_storage = CacheStorage(
        redis_url=os.getenv("REDIS_URL", "redis://redis:6379")
    )
    
    # Inicializace memory manageru
    memory_manager = MemoryManager(
        postgres_storage=postgres_storage,
        vector_storage=vector_storage,
        cache_storage=cache_storage
    )
    
    await memory_manager.initialize()
    logger.info("Memory Context Processor úspěšně spuštěn")
    
    yield
    
    logger.info("Ukončování Memory Context Processor...")
    if memory_manager:
        await memory_manager.cleanup()

# Vytvoření FastAPI aplikace
app = FastAPI(
    title="NESTOR Memory Context Processor",
    description="Služba pro správu a organizaci paměti a kontextu digitálních osobností",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # V produkci nastavit konkrétní domény
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_memory_manager() -> MemoryManager:
    """Dependency pro získání memory manageru"""
    if memory_manager is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Memory manager není inicializován"
        )
    return memory_manager

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        services={
            "postgres": "connected",
            "redis": "connected",
            "vector_storage": "ready"
        }
    )

@app.get("/metrics")
async def metrics():
    """Prometheus metriky"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@app.post("/memory/add", response_model=MemoryResponse)
async def add_memory(
    memory_data: MemoryCreate,
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Přidání nové paměti"""
    REQUEST_COUNT.labels(method="POST", endpoint="/memory/add").inc()
    
    try:
        with REQUEST_DURATION.time():
            memory = await manager.add_memory(
                content=memory_data.content,
                memory_type=memory_data.memory_type,
                personality_id=memory_data.personality_id,
                metadata=memory_data.metadata,
                importance=memory_data.importance
            )
            
            logger.info("Paměť přidána", memory_id=memory.id, type=memory.memory_type.value)
            return MemoryResponse(**memory.dict())
            
    except Exception as e:
        logger.error("Chyba při přidávání paměti", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při přidávání paměti: {str(e)}"
        )

@app.post("/memory/query", response_model=List[MemoryResponse])
async def query_memories(
    query_data: MemoryQuery,
    manager: MemoryManager = Depends(get_memory_manager)
):
    """Vyhledávání v paměti"""
    REQUEST_COUNT.labels(method="POST", endpoint="/memory/query").inc()
    
    try:
        with REQUEST_DURATION.time():
            memories = await manager.query_memories(
                query=query_data.query,
                memory_types=query_data.memory_types,
                personality_id=query_data.personality_id,
                top_k=query_data.top_k,
                similarity_threshold=query_data.similarity_threshold
            )
            
            logger.info("Paměti vyhledány", count=len(memories), query=query_data.query)
            return [MemoryResponse(**memory.dict()) for memory in memories]
            
    except Exception as e:
        logger.error("Chyba při vyhledávání pamětí", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při vyhledávání pamětí: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
