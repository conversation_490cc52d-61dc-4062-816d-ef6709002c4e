"""
Definice typů paměti a základn<PERSON>ch datových struktur pro MCP
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import uuid

class MemoryType(str, Enum):
    """Typy paměti podle dokumentace NESTOR"""
    SHORT_TERM = "short_term"      # Krátkodobá paměť (konverzační kontext)
    LONG_TERM = "long_term"        # Dlouhodobá paměť (fakta, znalosti)
    EPISODIC = "episodic"          # Epizodická paměť (události, zkušenosti)
    PROCEDURAL = "procedural"      # Procedurální paměť (dovednosti, postupy)
    SEMANTIC = "semantic"          # Sémantická paměť (obecné znalosti)
    EMOTIONAL = "emotional"        # Emoční pam<PERSON>ť (emoční vazby a reakce)

class Memory(BaseModel):
    """Základní model paměti"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    content: str = Field(..., description="Obsah paměti")
    memory_type: MemoryType = Field(..., description="Typ paměti")
    personality_id: Optional[str] = Field(None, description="ID digitální osobnosti")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Dodatečná metadata")
    importance: float = Field(default=0.5, ge=0.0, le=1.0, description="Důležitost paměti (0-1)")
    
    # Vektorová reprezentace
    embedding: Optional[List[float]] = Field(None, description="Vektorová reprezentace obsahu")
    
    # Časové značky
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    accessed_at: Optional[datetime] = Field(None, description="Poslední přístup")
    
    # Vztahy a kontext
    parent_memory_id: Optional[str] = Field(None, description="ID nadřazené paměti")
    related_memory_ids: List[str] = Field(default_factory=list, description="ID souvisejících pamětí")
    
    # Životní cyklus
    expires_at: Optional[datetime] = Field(None, description="Datum expirace")
    is_active: bool = Field(default=True, description="Je paměť aktivní")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class MemoryContext(BaseModel):
    """Kontext pro práci s pamětí"""
    personality_id: str
    conversation_id: Optional[str] = None
    session_id: Optional[str] = None
    current_topic: Optional[str] = None
    emotional_state: Optional[str] = None
    context_window: int = Field(default=10, description="Velikost kontextového okna")

class MemoryQuery(BaseModel):
    """Dotaz pro vyhledávání v paměti"""
    query_text: str
    memory_types: Optional[List[MemoryType]] = None
    personality_id: Optional[str] = None
    context: Optional[MemoryContext] = None
    
    # Parametry vyhledávání
    top_k: int = Field(default=5, ge=1, le=50)
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    importance_threshold: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Časové filtry
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    
    # Další filtry
    include_inactive: bool = Field(default=False)
    include_expired: bool = Field(default=False)

class MemorySearchResult(BaseModel):
    """Výsledek vyhledávání v paměti"""
    memory: Memory
    similarity_score: float
    relevance_score: float
    context_match: bool = False

class MemoryStats(BaseModel):
    """Statistiky paměti"""
    total_memories: int
    memories_by_type: Dict[MemoryType, int]
    average_importance: float
    most_accessed: List[str]
    recent_memories: List[str]
    memory_size_mb: float

class MemoryExport(BaseModel):
    """Export paměti"""
    personality_id: str
    export_format: str = Field(default="json", description="Formát exportu")
    include_embeddings: bool = Field(default=False)
    include_metadata: bool = Field(default=True)
    memory_types: Optional[List[MemoryType]] = None
    date_range: Optional[tuple[datetime, datetime]] = None

class MemoryImport(BaseModel):
    """Import paměti"""
    personality_id: str
    data: Dict[str, Any]
    merge_strategy: str = Field(default="append", description="Strategie slučování")
    validate_embeddings: bool = Field(default=True)
    update_existing: bool = Field(default=False)
