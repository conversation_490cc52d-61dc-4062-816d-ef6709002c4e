"""
Memory Service - základní implementace pro MCP
"""

from typing import List, Optional, Dict, Any
import structlog
from ..core.memory_manager import MemoryManager
from ..core.memory_types import Memory, MemoryType, MemoryQuery, MemorySearchResult

logger = structlog.get_logger()

class MemoryService:
    """Služba pro správu pamětí"""
    
    def __init__(self, memory_manager: MemoryManager):
        self.memory_manager = memory_manager
    
    async def add_memory(
        self,
        content: str,
        memory_type: MemoryType,
        personality_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        importance: float = 0.5
    ) -> Memory:
        """Přidání nové paměti"""
        return await self.memory_manager.add_memory(
            content=content,
            memory_type=memory_type,
            personality_id=personality_id,
            metadata=metadata,
            importance=importance
        )
    
    async def get_memory(self, memory_id: str) -> Optional[Memory]:
        """<PERSON>ísk<PERSON><PERSON> pam<PERSON> podle <PERSON>"""
        return await self.memory_manager.get_memory(memory_id)
    
    async def query_memories(
        self,
        query: str,
        memory_types: Optional[List[MemoryType]] = None,
        personality_id: Optional[str] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[MemorySearchResult]:
        """Vyhledávání pamětí"""
        return await self.memory_manager.query_memories(
            query=query,
            memory_types=memory_types,
            personality_id=personality_id,
            top_k=top_k,
            similarity_threshold=similarity_threshold
        )
    
    async def update_memory(
        self,
        memory_id: str,
        content: Optional[str] = None,
        importance: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Memory]:
        """Aktualizace paměti"""
        return await self.memory_manager.update_memory(
            memory_id=memory_id,
            content=content,
            importance=importance,
            metadata=metadata
        )
    
    async def delete_memory(self, memory_id: str) -> bool:
        """Smazání paměti"""
        return await self.memory_manager.delete_memory(memory_id)
