"""
PostgreSQL storage pro Memory Context Processor
"""

import json
from datetime import datetime
from typing import List, Optional, Dict, Any
import asyncpg
import structlog
from ..core.memory_types import Memory, MemoryType

logger = structlog.get_logger()

class PostgresStorage:
    """PostgreSQL storage pro paměti"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.pool = None
    
    async def initialize(self):
        """Inicializace connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # Vytvořen<PERSON> tabulek
            await self._create_tables()
            logger.info("PostgreSQL storage inicializován")
            
        except Exception as e:
            logger.error("Chyba při inicializaci PostgreSQL", error=str(e))
            raise
    
    async def cleanup(self):
        """Uzavření connection pool"""
        if self.pool:
            await self.pool.close()
    
    async def _create_tables(self):
        """Vytvoření databázov<PERSON><PERSON> tabulek"""
        async with self.pool.acquire() as conn:
            # Tabulka pro paměti
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id VARCHAR(36) PRIMARY KEY,
                    content TEXT NOT NULL,
                    memory_type VARCHAR(20) NOT NULL,
                    personality_id VARCHAR(36),
                    metadata JSONB DEFAULT '{}',
                    importance FLOAT DEFAULT 0.5,
                    parent_memory_id VARCHAR(36),
                    related_memory_ids JSONB DEFAULT '[]',
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW(),
                    accessed_at TIMESTAMP,
                    FOREIGN KEY (parent_memory_id) REFERENCES memories(id)
                );
            """)
            
            # Indexy pro optimalizaci
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memories_personality_id 
                ON memories(personality_id);
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memories_memory_type 
                ON memories(memory_type);
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memories_created_at 
                ON memories(created_at);
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memories_importance 
                ON memories(importance);
            """)
            
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memories_is_active 
                ON memories(is_active);
            """)
    
    async def store_memory(self, memory: Memory) -> None:
        """Uložení paměti do databáze"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO memories (
                    id, content, memory_type, personality_id, metadata,
                    importance, parent_memory_id, related_memory_ids,
                    expires_at, is_active, created_at, updated_at, accessed_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
            """,
                memory.id,
                memory.content,
                memory.memory_type.value,
                memory.personality_id,
                json.dumps(memory.metadata),
                memory.importance,
                memory.parent_memory_id,
                json.dumps(memory.related_memory_ids),
                memory.expires_at,
                memory.is_active,
                memory.created_at,
                memory.updated_at,
                memory.accessed_at
            )
    
    async def get_memory(self, memory_id: str) -> Optional[Memory]:
        """Získání paměti podle ID"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM memories WHERE id = $1
            """, memory_id)
            
            if not row:
                return None
            
            return self._row_to_memory(row)
    
    async def update_memory(self, memory: Memory) -> None:
        """Aktualizace paměti"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                UPDATE memories SET
                    content = $2,
                    memory_type = $3,
                    personality_id = $4,
                    metadata = $5,
                    importance = $6,
                    parent_memory_id = $7,
                    related_memory_ids = $8,
                    expires_at = $9,
                    is_active = $10,
                    updated_at = $11,
                    accessed_at = $12
                WHERE id = $1
            """,
                memory.id,
                memory.content,
                memory.memory_type.value,
                memory.personality_id,
                json.dumps(memory.metadata),
                memory.importance,
                memory.parent_memory_id,
                json.dumps(memory.related_memory_ids),
                memory.expires_at,
                memory.is_active,
                memory.updated_at,
                memory.accessed_at
            )
    
    async def get_memories_by_personality(
        self,
        personality_id: str,
        memory_types: Optional[List[MemoryType]] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Memory]:
        """Získání pamětí podle personality_id"""
        async with self.pool.acquire() as conn:
            query = """
                SELECT * FROM memories 
                WHERE personality_id = $1 AND is_active = TRUE
            """
            params = [personality_id]
            
            if memory_types:
                query += " AND memory_type = ANY($2)"
                params.append([mt.value for mt in memory_types])
            
            query += " ORDER BY created_at DESC LIMIT $" + str(len(params) + 1)
            params.append(limit)
            
            if offset > 0:
                query += " OFFSET $" + str(len(params) + 1)
                params.append(offset)
            
            rows = await conn.fetch(query, *params)
            return [self._row_to_memory(row) for row in rows]
    
    async def get_expired_memories(self) -> List[Memory]:
        """Získání expirovaných pamětí"""
        async with self.pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM memories 
                WHERE expires_at IS NOT NULL 
                AND expires_at < NOW() 
                AND is_active = TRUE
            """)
            return [self._row_to_memory(row) for row in rows]
    
    async def get_memory_stats(self, personality_id: Optional[str] = None) -> Dict[str, Any]:
        """Získání statistik pamětí"""
        async with self.pool.acquire() as conn:
            base_query = "FROM memories WHERE is_active = TRUE"
            params = []
            
            if personality_id:
                base_query += " AND personality_id = $1"
                params.append(personality_id)
            
            # Celkový počet
            total = await conn.fetchval(f"SELECT COUNT(*) {base_query}", *params)
            
            # Počet podle typu
            type_counts = await conn.fetch(f"""
                SELECT memory_type, COUNT(*) as count 
                {base_query} 
                GROUP BY memory_type
            """, *params)
            
            # Průměrná důležitost
            avg_importance = await conn.fetchval(f"""
                SELECT AVG(importance) {base_query}
            """, *params) or 0.0
            
            # Nejčastěji přistupované
            most_accessed = await conn.fetch(f"""
                SELECT id, content, accessed_at 
                {base_query} 
                AND accessed_at IS NOT NULL 
                ORDER BY accessed_at DESC 
                LIMIT 10
            """, *params)
            
            return {
                "total_memories": total,
                "memories_by_type": {row["memory_type"]: row["count"] for row in type_counts},
                "average_importance": float(avg_importance),
                "most_accessed": [row["id"] for row in most_accessed],
                "recent_memories": []  # TODO: implementovat
            }
    
    def _row_to_memory(self, row) -> Memory:
        """Konverze databázového řádku na Memory objekt"""
        return Memory(
            id=row["id"],
            content=row["content"],
            memory_type=MemoryType(row["memory_type"]),
            personality_id=row["personality_id"],
            metadata=json.loads(row["metadata"]) if row["metadata"] else {},
            importance=row["importance"],
            embedding=None,  # Embedding se načítá z vector storage
            created_at=row["created_at"],
            updated_at=row["updated_at"],
            accessed_at=row["accessed_at"],
            parent_memory_id=row["parent_memory_id"],
            related_memory_ids=json.loads(row["related_memory_ids"]) if row["related_memory_ids"] else [],
            expires_at=row["expires_at"],
            is_active=row["is_active"]
        )
