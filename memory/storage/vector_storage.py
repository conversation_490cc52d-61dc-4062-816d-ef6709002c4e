"""
Vector storage pro Memory Context Processor s pgvector
"""

import json
from typing import List, Optional, Dict, Any
import asyncpg
import structlog
import numpy as np

logger = structlog.get_logger()

class VectorStorage:
    """Vector storage s pgvector pro embeddings"""
    
    def __init__(self, database_url: str, vector_dimension: int = 384):
        self.database_url = database_url
        self.vector_dimension = vector_dimension
        self.pool = None
    
    async def initialize(self):
        """Inicializace vector storage"""
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            
            # Vytvoření tabulek a rozšíření
            await self._create_tables()
            logger.info("Vector storage inicializován")
            
        except Exception as e:
            logger.error("Chyba při inicializaci vector storage", error=str(e))
            raise
    
    async def cleanup(self):
        """Uzavření connection pool"""
        if self.pool:
            await self.pool.close()
    
    async def _create_tables(self):
        """Vytvoření tabulek pro vektorové úlož<PERSON>ště"""
        async with self.pool.acquire() as conn:
            # Povolení pgvector rozšíření
            await conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Tabulka pro embeddings
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS memory_embeddings (
                    memory_id VARCHAR(36) PRIMARY KEY,
                    embedding vector({self.vector_dimension}) NOT NULL,
                    metadata JSONB DEFAULT '{{}}',
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
            """)
            
            # Index pro vektorové vyhledávání
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memory_embeddings_cosine 
                ON memory_embeddings USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100);
            """)
            
            # Index pro metadata
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_memory_embeddings_metadata 
                ON memory_embeddings USING gin (metadata);
            """)
    
    async def store_embedding(
        self,
        memory_id: str,
        embedding: List[float],
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Uložení embedding do vektorové databáze"""
        
        if len(embedding) != self.vector_dimension:
            raise ValueError(f"Embedding dimension {len(embedding)} != {self.vector_dimension}")
        
        async with self.pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO memory_embeddings (memory_id, embedding, metadata)
                VALUES ($1, $2, $3)
                ON CONFLICT (memory_id) 
                DO UPDATE SET 
                    embedding = EXCLUDED.embedding,
                    metadata = EXCLUDED.metadata,
                    updated_at = NOW()
            """,
                memory_id,
                embedding,
                json.dumps(metadata or {})
            )
    
    async def get_embedding(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """Získání embedding podle memory_id"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT memory_id, embedding, metadata, created_at, updated_at
                FROM memory_embeddings 
                WHERE memory_id = $1
            """, memory_id)
            
            if not row:
                return None
            
            return {
                "memory_id": row["memory_id"],
                "embedding": list(row["embedding"]),
                "metadata": json.loads(row["metadata"]),
                "created_at": row["created_at"],
                "updated_at": row["updated_at"]
            }
    
    async def search_similar(
        self,
        query_embedding: List[float],
        top_k: int = 10,
        similarity_threshold: float = 0.7,
        metadata_filter: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Vyhledávání podobných embeddings"""
        
        if len(query_embedding) != self.vector_dimension:
            raise ValueError(f"Query embedding dimension {len(query_embedding)} != {self.vector_dimension}")
        
        async with self.pool.acquire() as conn:
            # Základní dotaz
            query = """
                SELECT 
                    memory_id,
                    embedding,
                    metadata,
                    1 - (embedding <=> $1) as similarity
                FROM memory_embeddings
                WHERE 1 - (embedding <=> $1) >= $2
            """
            params = [query_embedding, similarity_threshold]
            
            # Přidání metadata filtrů
            if metadata_filter:
                filter_conditions = []
                param_index = 3
                
                for key, value in metadata_filter.items():
                    if value is not None:
                        if isinstance(value, list):
                            # Pro pole hodnot (např. memory_types)
                            filter_conditions.append(f"metadata->>'{key}' = ANY(${param_index})")
                            params.append(value)
                        else:
                            # Pro jednotlivé hodnoty
                            filter_conditions.append(f"metadata->>'{key}' = ${param_index}")
                            params.append(str(value))
                        param_index += 1
                
                if filter_conditions:
                    query += " AND " + " AND ".join(filter_conditions)
            
            query += f" ORDER BY similarity DESC LIMIT ${len(params) + 1}"
            params.append(top_k)
            
            rows = await conn.fetch(query, *params)
            
            results = []
            for row in rows:
                results.append({
                    "memory_id": row["memory_id"],
                    "embedding": list(row["embedding"]),
                    "metadata": json.loads(row["metadata"]),
                    "similarity": float(row["similarity"])
                })
            
            return results
    
    async def update_embedding(
        self,
        memory_id: str,
        embedding: List[float],
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Aktualizace embedding"""
        
        if len(embedding) != self.vector_dimension:
            raise ValueError(f"Embedding dimension {len(embedding)} != {self.vector_dimension}")
        
        async with self.pool.acquire() as conn:
            if metadata is not None:
                await conn.execute("""
                    UPDATE memory_embeddings 
                    SET embedding = $2, metadata = $3, updated_at = NOW()
                    WHERE memory_id = $1
                """, memory_id, embedding, json.dumps(metadata))
            else:
                await conn.execute("""
                    UPDATE memory_embeddings 
                    SET embedding = $2, updated_at = NOW()
                    WHERE memory_id = $1
                """, memory_id, embedding)
    
    async def delete_embedding(self, memory_id: str) -> None:
        """Smazání embedding"""
        async with self.pool.acquire() as conn:
            await conn.execute("""
                DELETE FROM memory_embeddings WHERE memory_id = $1
            """, memory_id)
    
    async def get_embeddings_by_metadata(
        self,
        metadata_filter: Dict[str, Any],
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Získání embeddings podle metadata"""
        async with self.pool.acquire() as conn:
            filter_conditions = []
            params = []
            
            for key, value in metadata_filter.items():
                if isinstance(value, list):
                    filter_conditions.append(f"metadata->>'{key}' = ANY(${len(params) + 1})")
                    params.append(value)
                else:
                    filter_conditions.append(f"metadata->>'{key}' = ${len(params) + 1}")
                    params.append(str(value))
            
            query = f"""
                SELECT memory_id, embedding, metadata, created_at, updated_at
                FROM memory_embeddings
                WHERE {' AND '.join(filter_conditions)}
                ORDER BY created_at DESC
                LIMIT ${len(params) + 1}
            """
            params.append(limit)
            
            rows = await conn.fetch(query, *params)
            
            results = []
            for row in rows:
                results.append({
                    "memory_id": row["memory_id"],
                    "embedding": list(row["embedding"]),
                    "metadata": json.loads(row["metadata"]),
                    "created_at": row["created_at"],
                    "updated_at": row["updated_at"]
                })
            
            return results
    
    async def get_vector_stats(self) -> Dict[str, Any]:
        """Získání statistik vektorového úložiště"""
        async with self.pool.acquire() as conn:
            # Celkový počet embeddings
            total_count = await conn.fetchval("""
                SELECT COUNT(*) FROM memory_embeddings
            """)
            
            # Velikost tabulky
            table_size = await conn.fetchval("""
                SELECT pg_size_pretty(pg_total_relation_size('memory_embeddings'))
            """)
            
            # Počet podle memory_type
            type_counts = await conn.fetch("""
                SELECT 
                    metadata->>'memory_type' as memory_type,
                    COUNT(*) as count
                FROM memory_embeddings
                WHERE metadata->>'memory_type' IS NOT NULL
                GROUP BY metadata->>'memory_type'
            """)
            
            return {
                "total_embeddings": total_count,
                "table_size": table_size,
                "embeddings_by_type": {row["memory_type"]: row["count"] for row in type_counts},
                "vector_dimension": self.vector_dimension
            }
