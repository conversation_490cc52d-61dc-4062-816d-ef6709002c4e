"""
Redis cache storage pro Memory Context Processor
"""

import json
import pickle
from typing import Optional, List, Dict, Any
import redis.asyncio as redis
import structlog
from ..core.memory_types import Memory

logger = structlog.get_logger()

class CacheStorage:
    """Redis cache pro rychlý přístup k pamětem"""
    
    def __init__(self, redis_url: str, ttl: int = 3600):
        self.redis_url = redis_url
        self.ttl = ttl  # Time to live v sekundách
        self.redis_client = None
    
    async def initialize(self):
        """Inicializace Redis klienta"""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=False  # Pro pickle data
            )
            
            # Test připojení
            await self.redis_client.ping()
            logger.info("Redis cache storage inicializován")
            
        except Exception as e:
            logger.error("Chyba při inicializaci Redis", error=str(e))
            raise
    
    async def cleanup(self):
        """Uzavření Redis připojení"""
        if self.redis_client:
            await self.redis_client.close()
    
    def _memory_key(self, memory_id: str) -> str:
        """Generování klíče pro paměť"""
        return f"memory:{memory_id}"
    
    def _personality_memories_key(self, personality_id: str) -> str:
        """Generování klíče pro paměti osobnosti"""
        return f"personality_memories:{personality_id}"
    
    def _recent_memories_key(self, personality_id: str) -> str:
        """Generování klíče pro nedávné paměti"""
        return f"recent_memories:{personality_id}"
    
    async def cache_memory(self, memory: Memory) -> None:
        """Uložení paměti do cache"""
        try:
            # Serializace memory objektu
            memory_data = pickle.dumps(memory)
            
            # Uložení s TTL
            await self.redis_client.setex(
                self._memory_key(memory.id),
                self.ttl,
                memory_data
            )
            
            # Přidání do seznamu pamětí osobnosti
            if memory.personality_id:
                await self.redis_client.lpush(
                    self._personality_memories_key(memory.personality_id),
                    memory.id
                )
                
                # Omezení velikosti seznamu
                await self.redis_client.ltrim(
                    self._personality_memories_key(memory.personality_id),
                    0, 99  # Posledních 100 pamětí
                )
                
                # Nastavení TTL pro seznam
                await self.redis_client.expire(
                    self._personality_memories_key(memory.personality_id),
                    self.ttl
                )
            
            # Přidání do nedávných pamětí
            if memory.personality_id:
                await self.redis_client.zadd(
                    self._recent_memories_key(memory.personality_id),
                    {memory.id: memory.created_at.timestamp()}
                )
                
                # Omezení velikosti sorted set
                await self.redis_client.zremrangebyrank(
                    self._recent_memories_key(memory.personality_id),
                    0, -51  # Ponechat posledních 50
                )
                
                # Nastavení TTL
                await self.redis_client.expire(
                    self._recent_memories_key(memory.personality_id),
                    self.ttl
                )
            
        except Exception as e:
            logger.error("Chyba při ukládání do cache", memory_id=memory.id, error=str(e))
    
    async def get_memory(self, memory_id: str) -> Optional[Memory]:
        """Získání paměti z cache"""
        try:
            memory_data = await self.redis_client.get(self._memory_key(memory_id))
            
            if memory_data:
                memory = pickle.loads(memory_data)
                return memory
            
            return None
            
        except Exception as e:
            logger.error("Chyba při čtení z cache", memory_id=memory_id, error=str(e))
            return None
    
    async def remove_memory(self, memory_id: str) -> None:
        """Odstranění paměti z cache"""
        try:
            await self.redis_client.delete(self._memory_key(memory_id))
            
        except Exception as e:
            logger.error("Chyba při odstraňování z cache", memory_id=memory_id, error=str(e))
    
    async def get_personality_memories(
        self,
        personality_id: str,
        limit: int = 20
    ) -> List[str]:
        """Získání seznamu memory_id pro osobnost"""
        try:
            memory_ids = await self.redis_client.lrange(
                self._personality_memories_key(personality_id),
                0, limit - 1
            )
            
            return [mid.decode('utf-8') if isinstance(mid, bytes) else mid for mid in memory_ids]
            
        except Exception as e:
            logger.error("Chyba při čtení pamětí osobnosti", personality_id=personality_id, error=str(e))
            return []
    
    async def get_recent_memories(
        self,
        personality_id: str,
        limit: int = 10
    ) -> List[str]:
        """Získání nedávných pamětí seřazených podle času"""
        try:
            # Získání z sorted set (nejnovější první)
            memory_ids = await self.redis_client.zrevrange(
                self._recent_memories_key(personality_id),
                0, limit - 1
            )
            
            return [mid.decode('utf-8') if isinstance(mid, bytes) else mid for mid in memory_ids]
            
        except Exception as e:
            logger.error("Chyba při čtení nedávných pamětí", personality_id=personality_id, error=str(e))
            return []
    
    async def cache_search_results(
        self,
        query_hash: str,
        results: List[Dict[str, Any]],
        ttl: int = 300  # 5 minut
    ) -> None:
        """Cache výsledků vyhledávání"""
        try:
            results_data = json.dumps(results, default=str)
            await self.redis_client.setex(
                f"search_results:{query_hash}",
                ttl,
                results_data
            )
            
        except Exception as e:
            logger.error("Chyba při ukládání výsledků vyhledávání", error=str(e))
    
    async def get_cached_search_results(
        self,
        query_hash: str
    ) -> Optional[List[Dict[str, Any]]]:
        """Získání cachovaných výsledků vyhledávání"""
        try:
            results_data = await self.redis_client.get(f"search_results:{query_hash}")
            
            if results_data:
                if isinstance(results_data, bytes):
                    results_data = results_data.decode('utf-8')
                return json.loads(results_data)
            
            return None
            
        except Exception as e:
            logger.error("Chyba při čtení výsledků vyhledávání", error=str(e))
            return None
    
    async def increment_access_count(self, memory_id: str) -> int:
        """Zvýšení počítadla přístupů k paměti"""
        try:
            count = await self.redis_client.incr(f"access_count:{memory_id}")
            
            # Nastavení TTL při prvním přístupu
            if count == 1:
                await self.redis_client.expire(f"access_count:{memory_id}", 86400)  # 24 hodin
            
            return count
            
        except Exception as e:
            logger.error("Chyba při zvyšování počítadla", memory_id=memory_id, error=str(e))
            return 0
    
    async def get_access_count(self, memory_id: str) -> int:
        """Získání počtu přístupů k paměti"""
        try:
            count = await self.redis_client.get(f"access_count:{memory_id}")
            return int(count) if count else 0
            
        except Exception as e:
            logger.error("Chyba při čtení počítadla", memory_id=memory_id, error=str(e))
            return 0
    
    async def clear_personality_cache(self, personality_id: str) -> None:
        """Vymazání cache pro konkrétní osobnost"""
        try:
            # Získání všech memory_id pro osobnost
            memory_ids = await self.get_personality_memories(personality_id, 1000)
            
            # Smazání jednotlivých pamětí
            if memory_ids:
                keys_to_delete = [self._memory_key(mid) for mid in memory_ids]
                await self.redis_client.delete(*keys_to_delete)
            
            # Smazání seznamů
            await self.redis_client.delete(
                self._personality_memories_key(personality_id),
                self._recent_memories_key(personality_id)
            )
            
        except Exception as e:
            logger.error("Chyba při mazání cache osobnosti", personality_id=personality_id, error=str(e))
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Získání statistik cache"""
        try:
            info = await self.redis_client.info()
            
            # Počet klíčů podle prefixu
            memory_keys = len(await self.redis_client.keys("memory:*"))
            personality_keys = len(await self.redis_client.keys("personality_memories:*"))
            search_keys = len(await self.redis_client.keys("search_results:*"))
            
            return {
                "redis_info": {
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0)
                },
                "cache_keys": {
                    "memory_keys": memory_keys,
                    "personality_keys": personality_keys,
                    "search_keys": search_keys
                }
            }
            
        except Exception as e:
            logger.error("Chyba při získávání statistik cache", error=str(e))
            return {}
