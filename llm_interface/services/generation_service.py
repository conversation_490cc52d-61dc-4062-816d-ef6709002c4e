"""
Generation Service - generování textu pomocí LLM
"""

from typing import List, Optional, Dict, Any, Union
import structlog
import httpx
import json

logger = structlog.get_logger()

class GenerationService:
    """Služba pro generování textu pomocí LLM"""
    
    def __init__(self, model_service, redis_url: str):
        self.model_service = model_service
        self.redis_url = redis_url
        self.http_client = None
    
    async def initialize(self):
        """Inicializace generation služby"""
        self.http_client = httpx.AsyncClient(timeout=60.0)
        logger.info("Generation Service inicializován")
    
    async def cleanup(self):
        """Úklid při ukončování"""
        if self.http_client:
            await self.http_client.aclose()
    
    async def generate(
        self,
        prompt: str,
        model: str = "gpt-3.5-turbo",
        max_tokens: int = 150,
        temperature: float = 0.7,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Generování textu pomocí LLM"""
        
        try:
            # Kontrola dostupnosti modelu
            available_models = await self.model_service.get_available_models()
            model_names = [m.name for m in available_models]
            
            if model not in model_names:
                # Fallback na dostupný model
                model = model_names[0] if model_names else "mock"
                logger.warning("Model není dostupný, používám fallback", requested=model, fallback=model)
            
            # Pro demo účely - mock implementace
            if model == "mock" or not self.model_service.openai_api_key:
                return await self._mock_generate(prompt, model, max_tokens)
            
            # OpenAI API volání
            if model.startswith("gpt"):
                return await self._openai_generate(
                    prompt, model, max_tokens, temperature, top_p,
                    frequency_penalty, presence_penalty, stop, stream
                )
            
            # Anthropic API volání
            elif model.startswith("claude"):
                return await self._anthropic_generate(
                    prompt, model, max_tokens, temperature, top_p, stop
                )
            
            # Local model
            else:
                return await self._local_generate(
                    prompt, model, max_tokens, temperature, stop
                )
                
        except Exception as e:
            logger.error("Chyba při generování textu", error=str(e), model=model)
            # Fallback na mock
            return await self._mock_generate(prompt, model, max_tokens)
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-3.5-turbo",
        max_tokens: int = 150,
        temperature: float = 0.7,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[List[str]] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Chat completion pomocí LLM"""
        
        try:
            # Pro demo účely - mock implementace
            if model == "mock" or not self.model_service.openai_api_key:
                return await self._mock_chat(messages, model, max_tokens)
            
            # OpenAI API volání
            if model.startswith("gpt"):
                return await self._openai_chat(
                    messages, model, max_tokens, temperature, top_p,
                    frequency_penalty, presence_penalty, stop, stream
                )
            
            # Anthropic API volání
            elif model.startswith("claude"):
                return await self._anthropic_chat(
                    messages, model, max_tokens, temperature, stop
                )
            
            # Local model
            else:
                return await self._local_chat(
                    messages, model, max_tokens, temperature, stop
                )
                
        except Exception as e:
            logger.error("Chyba při chat completion", error=str(e), model=model)
            # Fallback na mock
            return await self._mock_chat(messages, model, max_tokens)
    
    async def _mock_generate(self, prompt: str, model: str, max_tokens: int) -> Dict[str, Any]:
        """Mock implementace pro generování"""
        
        # Jednoduchá mock odpověď
        if "hello" in prompt.lower():
            text = "Hello! How can I help you today?"
        elif "what" in prompt.lower():
            text = "That's an interesting question. Let me think about it..."
        elif "nestor" in prompt.lower():
            text = "NESTOR is an innovative platform for creating and tokenizing digital personalities using advanced AI technologies."
        else:
            text = f"This is a mock response to your prompt. You asked about: {prompt[:50]}..."
        
        return {
            "text": text,
            "model": f"{model} (mock)",
            "usage": {
                "prompt_tokens": len(prompt.split()),
                "completion_tokens": len(text.split()),
                "total_tokens": len(prompt.split()) + len(text.split())
            },
            "metadata": {
                "mock": True,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }
    
    async def _mock_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int) -> Dict[str, Any]:
        """Mock implementace pro chat"""
        
        last_message = messages[-1]["content"] if messages else ""
        
        # Jednoduchá mock odpověď
        if "hello" in last_message.lower():
            content = "Hello! I'm a digital personality. How can I assist you?"
        elif "how are you" in last_message.lower():
            content = "I'm doing well, thank you for asking! I'm here to help you with any questions."
        elif "nestor" in last_message.lower():
            content = "NESTOR is a fascinating project that combines AI, blockchain, and VR technologies to create authentic digital personalities."
        else:
            content = f"I understand you're asking about: {last_message[:50]}... Let me help you with that."
        
        return {
            "message": {
                "role": "assistant",
                "content": content
            },
            "model": f"{model} (mock)",
            "usage": {
                "prompt_tokens": sum(len(msg["content"].split()) for msg in messages),
                "completion_tokens": len(content.split()),
                "total_tokens": sum(len(msg["content"].split()) for msg in messages) + len(content.split())
            },
            "metadata": {
                "mock": True,
                "timestamp": "2024-01-01T00:00:00Z"
            }
        }
    
    async def _openai_generate(self, prompt: str, model: str, max_tokens: int, 
                              temperature: float, top_p: float, frequency_penalty: float,
                              presence_penalty: float, stop: Optional[List[str]], stream: bool) -> Dict[str, Any]:
        """OpenAI API generování"""
        # TODO: Implementovat skutečné OpenAI API volání
        return await self._mock_generate(prompt, model, max_tokens)
    
    async def _openai_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int,
                          temperature: float, top_p: float, frequency_penalty: float,
                          presence_penalty: float, stop: Optional[List[str]], stream: bool) -> Dict[str, Any]:
        """OpenAI API chat completion"""
        # TODO: Implementovat skutečné OpenAI API volání
        return await self._mock_chat(messages, model, max_tokens)
    
    async def _anthropic_generate(self, prompt: str, model: str, max_tokens: int,
                                 temperature: float, top_p: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Anthropic API generování"""
        # TODO: Implementovat skutečné Anthropic API volání
        return await self._mock_generate(prompt, model, max_tokens)
    
    async def _anthropic_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int,
                             temperature: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Anthropic API chat completion"""
        # TODO: Implementovat skutečné Anthropic API volání
        return await self._mock_chat(messages, model, max_tokens)
    
    async def _local_generate(self, prompt: str, model: str, max_tokens: int,
                             temperature: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Local model generování"""
        # TODO: Implementovat lokální model
        return await self._mock_generate(prompt, model, max_tokens)
    
    async def _local_chat(self, messages: List[Dict[str, str]], model: str, max_tokens: int,
                         temperature: float, stop: Optional[List[str]]) -> Dict[str, Any]:
        """Local model chat completion"""
        # TODO: Implementovat lokální model
        return await self._mock_chat(messages, model, max_tokens)
