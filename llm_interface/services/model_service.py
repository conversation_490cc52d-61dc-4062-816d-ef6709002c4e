"""
Model Service - spr<PERSON>va dostupných modelů
"""

from typing import List, Optional, Dict, Any
import structlog
from ..main import ModelInfo, ModelProvider, ModelType

logger = structlog.get_logger()

class ModelService:
    """Služba pro správu modelů"""
    
    def __init__(
        self,
        openai_api_key: Optional[str] = None,
        anthropic_api_key: Optional[str] = None,
        huggingface_api_key: Optional[str] = None,
        local_model_path: str = "/app/models"
    ):
        self.openai_api_key = openai_api_key
        self.anthropic_api_key = anthropic_api_key
        self.huggingface_api_key = huggingface_api_key
        self.local_model_path = local_model_path
        self.available_models = []
    
    async def initialize(self):
        """Inicializace model služby"""
        await self._discover_models()
        logger.info("Model Service inicializován", models_count=len(self.available_models))
    
    async def cleanup(self):
        """Úklid při <PERSON>"""
        pass
    
    async def _discover_models(self):
        """Zjištění dostupných modelů"""
        self.available_models = []
        
        # OpenAI modely
        if self.openai_api_key:
            self.available_models.extend([
                ModelInfo(
                    name="gpt-3.5-turbo",
                    provider=ModelProvider.OPENAI,
                    type=ModelType.CHAT,
                    max_tokens=4096,
                    available=True,
                    description="OpenAI GPT-3.5 Turbo model for chat completion"
                ),
                ModelInfo(
                    name="gpt-4",
                    provider=ModelProvider.OPENAI,
                    type=ModelType.CHAT,
                    max_tokens=8192,
                    available=True,
                    description="OpenAI GPT-4 model for chat completion"
                ),
                ModelInfo(
                    name="text-embedding-ada-002",
                    provider=ModelProvider.OPENAI,
                    type=ModelType.EMBEDDING,
                    max_tokens=8191,
                    available=True,
                    description="OpenAI Ada embedding model"
                )
            ])
        
        # Anthropic modely
        if self.anthropic_api_key:
            self.available_models.extend([
                ModelInfo(
                    name="claude-3-sonnet-20240229",
                    provider=ModelProvider.ANTHROPIC,
                    type=ModelType.CHAT,
                    max_tokens=4096,
                    available=True,
                    description="Anthropic Claude 3 Sonnet model"
                ),
                ModelInfo(
                    name="claude-3-haiku-20240307",
                    provider=ModelProvider.ANTHROPIC,
                    type=ModelType.CHAT,
                    max_tokens=4096,
                    available=True,
                    description="Anthropic Claude 3 Haiku model"
                )
            ])
        
        # Lokální modely (vždy dostupné pro demo)
        self.available_models.extend([
            ModelInfo(
                name="all-MiniLM-L6-v2",
                provider=ModelProvider.LOCAL,
                type=ModelType.EMBEDDING,
                max_tokens=512,
                available=True,
                description="Local sentence-transformers embedding model"
            ),
            ModelInfo(
                name="mock",
                provider=ModelProvider.LOCAL,
                type=ModelType.CHAT,
                max_tokens=2048,
                available=True,
                description="Mock model for testing and demo purposes"
            )
        ])
    
    async def get_available_models(self) -> List[ModelInfo]:
        """Získání seznamu dostupných modelů"""
        return self.available_models
    
    async def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """Získání informací o konkrétním modelu"""
        for model in self.available_models:
            if model.name == model_name:
                return model
        return None
    
    async def is_model_available(self, model_name: str) -> bool:
        """Kontrola dostupnosti modelu"""
        model_info = await self.get_model_info(model_name)
        return model_info is not None and model_info.available
    
    async def get_models_by_type(self, model_type: ModelType) -> List[ModelInfo]:
        """Získání modelů podle typu"""
        return [model for model in self.available_models if model.type == model_type]
    
    async def get_models_by_provider(self, provider: ModelProvider) -> List[ModelInfo]:
        """Získání modelů podle poskytovatele"""
        return [model for model in self.available_models if model.provider == provider]
    
    async def get_default_model(self, model_type: ModelType) -> Optional[ModelInfo]:
        """Získání výchozího modelu pro daný typ"""
        models = await self.get_models_by_type(model_type)
        
        if not models:
            return None
        
        # Preferované pořadí: OpenAI -> Anthropic -> Local
        for provider in [ModelProvider.OPENAI, ModelProvider.ANTHROPIC, ModelProvider.LOCAL]:
            for model in models:
                if model.provider == provider and model.available:
                    return model
        
        # Fallback na první dostupný model
        return models[0] if models else None
