"""
Embedding Service - vytváření embeddings pro text
"""

from typing import List, Union, Dict, Any
import structlog
from sentence_transformers import SentenceTransformer
import numpy as np

logger = structlog.get_logger()

class EmbeddingService:
    """Služba pro vytváření embeddings"""
    
    def __init__(self, model_service, redis_url: str):
        self.model_service = model_service
        self.redis_url = redis_url
        self.local_models = {}
    
    async def initialize(self):
        """Inicializace embedding služby"""
        try:
            # Načtení lokálního embedding modelu
            self.local_models["all-MiniLM-L6-v2"] = SentenceTransformer("all-MiniLM-L6-v2")
            logger.info("Embedding Service inicializován")
        except Exception as e:
            logger.error("Chyba při inicializaci Embedding Service", error=str(e))
            # Pokračovat bez lokálních modelů
    
    async def cleanup(self):
        """Úklid při ukončování"""
        pass
    
    async def embed(
        self,
        text: Union[str, List[str]],
        model: str = "text-embedding-ada-002",
        normalize: bool = True
    ) -> Dict[str, Any]:
        """Vytvoření embeddings pro text"""
        
        try:
            # Převod na seznam pokud je vstup string
            texts = [text] if isinstance(text, str) else text
            
            # Kontrola dostupnosti modelu
            if model in self.local_models:
                return await self._local_embed(texts, model, normalize)
            elif model.startswith("text-embedding"):
                return await self._openai_embed(texts, model, normalize)
            else:
                # Fallback na lokální model
                return await self._local_embed(texts, "all-MiniLM-L6-v2", normalize)
                
        except Exception as e:
            logger.error("Chyba při vytváření embeddings", error=str(e), model=model)
            # Fallback na mock
            return await self._mock_embed(texts, model)
    
    async def _local_embed(self, texts: List[str], model: str, normalize: bool) -> Dict[str, Any]:
        """Lokální embedding pomocí sentence-transformers"""
        
        if model not in self.local_models:
            # Pokus o načtení modelu
            try:
                self.local_models[model] = SentenceTransformer(model)
            except Exception as e:
                logger.error("Chyba při načítání modelu", error=str(e), model=model)
                return await self._mock_embed(texts, model)
        
        embedding_model = self.local_models[model]
        
        # Generování embeddings
        embeddings = embedding_model.encode(texts)
        
        # Normalizace pokud je požadována
        if normalize:
            embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        
        # Převod na seznam
        embeddings_list = [emb.tolist() for emb in embeddings]
        
        return {
            "embeddings": embeddings_list,
            "model": model,
            "usage": {
                "prompt_tokens": sum(len(text.split()) for text in texts),
                "total_tokens": sum(len(text.split()) for text in texts)
            },
            "metadata": {
                "dimension": len(embeddings_list[0]) if embeddings_list else 0,
                "normalized": normalize,
                "local_model": True
            }
        }
    
    async def _openai_embed(self, texts: List[str], model: str, normalize: bool) -> Dict[str, Any]:
        """OpenAI API embedding"""
        # TODO: Implementovat skutečné OpenAI API volání
        return await self._mock_embed(texts, model)
    
    async def _mock_embed(self, texts: List[str], model: str) -> Dict[str, Any]:
        """Mock implementace pro embeddings"""
        
        # Generování mock embeddings (náhodné vektory)
        dimension = 384  # Standardní dimenze pro all-MiniLM-L6-v2
        embeddings = []
        
        for text in texts:
            # Pseudo-náhodný vektor založený na hash textu
            import hashlib
            text_hash = hashlib.md5(text.encode()).hexdigest()
            
            # Převod hash na čísla
            embedding = []
            for i in range(0, min(len(text_hash), dimension * 2), 2):
                hex_pair = text_hash[i:i+2]
                value = int(hex_pair, 16) / 255.0 - 0.5  # Normalizace na [-0.5, 0.5]
                embedding.append(value)
            
            # Doplnění do požadované dimenze
            while len(embedding) < dimension:
                embedding.append(0.0)
            
            # Oříznutí na požadovanou dimenzi
            embedding = embedding[:dimension]
            
            # Normalizace
            norm = sum(x*x for x in embedding) ** 0.5
            if norm > 0:
                embedding = [x/norm for x in embedding]
            
            embeddings.append(embedding)
        
        return {
            "embeddings": embeddings,
            "model": f"{model} (mock)",
            "usage": {
                "prompt_tokens": sum(len(text.split()) for text in texts),
                "total_tokens": sum(len(text.split()) for text in texts)
            },
            "metadata": {
                "dimension": dimension,
                "normalized": True,
                "mock": True
            }
        }
