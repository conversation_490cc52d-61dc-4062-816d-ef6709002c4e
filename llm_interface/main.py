"""
NESTOR LLM Interface Service
Služba pro interakci s velk<PERSON><PERSON> j<PERSON> modely
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import List, Optional, Dict, Any, Union
from enum import Enum

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import structlog
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

from services.generation_service import GenerationService
from services.embedding_service import EmbeddingService
from services.model_service import ModelService

# Konfigurace loggingu
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Metriky
REQUEST_COUNT = Counter('llm_requests_total', 'Total requests', ['method', 'endpoint', 'model'])
REQUEST_DURATION = Histogram('llm_request_duration_seconds', 'Request duration')
TOKEN_COUNT = Counter('llm_tokens_total', 'Total tokens processed', ['type', 'model'])

# Enums
class ModelProvider(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"
    HUGGINGFACE = "huggingface"

class ModelType(str, Enum):
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"

# Pydantic modely
class GenerationRequest(BaseModel):
    prompt: str = Field(..., description="Vstupní prompt")
    model: str = Field(default="gpt-3.5-turbo", description="Model k použití")
    max_tokens: int = Field(default=150, ge=1, le=4000, description="Maximální počet tokenů")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Teplota generování")
    top_p: float = Field(default=1.0, ge=0.0, le=1.0, description="Top-p sampling")
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0, description="Presence penalty")
    stop: Optional[List[str]] = Field(None, description="Stop sekvence")
    stream: bool = Field(default=False, description="Streamování odpovědi")

class ChatMessage(BaseModel):
    role: str = Field(..., description="Role zprávy (system, user, assistant)")
    content: str = Field(..., description="Obsah zprávy")

class ChatRequest(BaseModel):
    messages: List[ChatMessage] = Field(..., description="Seznam zpráv konverzace")
    model: str = Field(default="gpt-3.5-turbo", description="Model k použití")
    max_tokens: int = Field(default=150, ge=1, le=4000, description="Maximální počet tokenů")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Teplota generování")
    top_p: float = Field(default=1.0, ge=0.0, le=1.0, description="Top-p sampling")
    frequency_penalty: float = Field(default=0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: float = Field(default=0.0, ge=-2.0, le=2.0, description="Presence penalty")
    stop: Optional[List[str]] = Field(None, description="Stop sekvence")
    stream: bool = Field(default=False, description="Streamování odpovědi")

class EmbeddingRequest(BaseModel):
    text: Union[str, List[str]] = Field(..., description="Text nebo seznam textů k embedování")
    model: str = Field(default="text-embedding-ada-002", description="Embedding model")
    normalize: bool = Field(default=True, description="Normalizovat embeddings")

class GenerationResponse(BaseModel):
    text: str = Field(..., description="Vygenerovaný text")
    model: str = Field(..., description="Použitý model")
    usage: Dict[str, int] = Field(..., description="Statistiky použití")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Dodatečná metadata")

class ChatResponse(BaseModel):
    message: ChatMessage = Field(..., description="Vygenerovaná zpráva")
    model: str = Field(..., description="Použitý model")
    usage: Dict[str, int] = Field(..., description="Statistiky použití")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Dodatečná metadata")

class EmbeddingResponse(BaseModel):
    embeddings: List[List[float]] = Field(..., description="Seznam embeddings")
    model: str = Field(..., description="Použitý model")
    usage: Dict[str, int] = Field(..., description="Statistiky použití")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Dodatečná metadata")

class ModelInfo(BaseModel):
    name: str
    provider: ModelProvider
    type: ModelType
    max_tokens: int
    available: bool
    description: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    version: str
    services: Dict[str, str]
    available_models: List[str]

# Globální proměnné
generation_service: Optional[GenerationService] = None
embedding_service: Optional[EmbeddingService] = None
model_service: Optional[ModelService] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifecycle management pro FastAPI aplikaci"""
    global generation_service, embedding_service, model_service
    
    logger.info("Spouštění LLM Interface Service...")
    
    # Inicializace služeb
    model_service = ModelService(
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        anthropic_api_key=os.getenv("ANTHROPIC_API_KEY"),
        huggingface_api_key=os.getenv("HUGGINGFACE_API_KEY"),
        local_model_path=os.getenv("LOCAL_MODEL_PATH", "/app/models")
    )
    
    generation_service = GenerationService(
        model_service=model_service,
        redis_url=os.getenv("REDIS_URL", "redis://redis:6379")
    )
    
    embedding_service = EmbeddingService(
        model_service=model_service,
        redis_url=os.getenv("REDIS_URL", "redis://redis:6379")
    )
    
    # Inicializace všech služeb
    await model_service.initialize()
    await generation_service.initialize()
    await embedding_service.initialize()
    
    logger.info("LLM Interface Service úspěšně spuštěn")
    
    yield
    
    logger.info("Ukončování LLM Interface Service...")
    if generation_service:
        await generation_service.cleanup()
    if embedding_service:
        await embedding_service.cleanup()
    if model_service:
        await model_service.cleanup()

# Vytvoření FastAPI aplikace
app = FastAPI(
    title="NESTOR LLM Interface Service",
    description="Služba pro interakci s velkými jazykovými modely",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # V produkci nastavit konkrétní domény
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_generation_service() -> GenerationService:
    """Dependency pro získání generation service"""
    if generation_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Generation service není inicializován"
        )
    return generation_service

def get_embedding_service() -> EmbeddingService:
    """Dependency pro získání embedding service"""
    if embedding_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Embedding service není inicializován"
        )
    return embedding_service

def get_model_service() -> ModelService:
    """Dependency pro získání model service"""
    if model_service is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Model service není inicializován"
        )
    return model_service

@app.get("/health", response_model=HealthResponse)
async def health_check(model_svc: ModelService = Depends(get_model_service)):
    """Health check endpoint"""
    available_models = await model_svc.get_available_models()
    
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        services={
            "redis": "connected",
            "model_service": "ready",
            "generation_service": "ready",
            "embedding_service": "ready"
        },
        available_models=[model.name for model in available_models]
    )

@app.get("/metrics")
async def metrics():
    """Prometheus metriky"""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

@app.post("/llm/generate", response_model=GenerationResponse)
async def generate_text(
    request: GenerationRequest,
    gen_service: GenerationService = Depends(get_generation_service)
):
    """Generování textu pomocí LLM"""
    REQUEST_COUNT.labels(method="POST", endpoint="/llm/generate", model=request.model).inc()
    
    try:
        with REQUEST_DURATION.time():
            response = await gen_service.generate(
                prompt=request.prompt,
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                frequency_penalty=request.frequency_penalty,
                presence_penalty=request.presence_penalty,
                stop=request.stop,
                stream=request.stream
            )
            
            # Aktualizace metrik
            TOKEN_COUNT.labels(type="input", model=request.model).inc(response["usage"]["prompt_tokens"])
            TOKEN_COUNT.labels(type="output", model=request.model).inc(response["usage"]["completion_tokens"])
            
            logger.info("Text vygenerován", model=request.model, tokens=response["usage"]["total_tokens"])
            
            return GenerationResponse(**response)
            
    except Exception as e:
        logger.error("Chyba při generování textu", error=str(e), model=request.model)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při generování textu: {str(e)}"
        )

@app.post("/llm/chat", response_model=ChatResponse)
async def chat_completion(
    request: ChatRequest,
    gen_service: GenerationService = Depends(get_generation_service)
):
    """Chat completion pomocí LLM"""
    REQUEST_COUNT.labels(method="POST", endpoint="/llm/chat", model=request.model).inc()
    
    try:
        with REQUEST_DURATION.time():
            response = await gen_service.chat(
                messages=[msg.dict() for msg in request.messages],
                model=request.model,
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                top_p=request.top_p,
                frequency_penalty=request.frequency_penalty,
                presence_penalty=request.presence_penalty,
                stop=request.stop,
                stream=request.stream
            )
            
            # Aktualizace metrik
            TOKEN_COUNT.labels(type="input", model=request.model).inc(response["usage"]["prompt_tokens"])
            TOKEN_COUNT.labels(type="output", model=request.model).inc(response["usage"]["completion_tokens"])
            
            logger.info("Chat dokončen", model=request.model, tokens=response["usage"]["total_tokens"])
            
            return ChatResponse(**response)
            
    except Exception as e:
        logger.error("Chyba při chat completion", error=str(e), model=request.model)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při chat completion: {str(e)}"
        )

@app.post("/llm/embed", response_model=EmbeddingResponse)
async def create_embeddings(
    request: EmbeddingRequest,
    emb_service: EmbeddingService = Depends(get_embedding_service)
):
    """Vytvoření embeddings pro text"""
    REQUEST_COUNT.labels(method="POST", endpoint="/llm/embed", model=request.model).inc()
    
    try:
        with REQUEST_DURATION.time():
            response = await emb_service.embed(
                text=request.text,
                model=request.model,
                normalize=request.normalize
            )
            
            # Aktualizace metrik
            text_count = len(request.text) if isinstance(request.text, list) else 1
            TOKEN_COUNT.labels(type="embedding", model=request.model).inc(response["usage"]["total_tokens"])
            
            logger.info("Embeddings vytvořeny", model=request.model, count=text_count)
            
            return EmbeddingResponse(**response)
            
    except Exception as e:
        logger.error("Chyba při vytváření embeddings", error=str(e), model=request.model)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při vytváření embeddings: {str(e)}"
        )

@app.get("/llm/models", response_model=List[ModelInfo])
async def list_models(model_svc: ModelService = Depends(get_model_service)):
    """Seznam dostupných modelů"""
    try:
        models = await model_svc.get_available_models()
        return models
        
    except Exception as e:
        logger.error("Chyba při získávání seznamu modelů", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chyba při získávání seznamu modelů: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
